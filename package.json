{"name": "foodbank4u", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"aos": "^2.3.4", "axios": "^1.8.1", "gsap": "^3.12.7", "react": "^18.3.1", "react-dom": "^18.3.1", "react-fast-marquee": "^1.6.5", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.4.1", "react-icons": "^5.4.0", "react-multi-carousel": "^2.8.5", "react-router-dom": "^7.1.1", "react-slick": "^0.30.3", "recharts": "^3.1.0", "slick-carousel": "^1.8.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "vite": "^6.0.5"}}