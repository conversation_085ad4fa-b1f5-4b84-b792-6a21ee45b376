import { Route, Routes } from "react-router-dom";
import LandingPage from "./pages/LandingPage/LandingPage";
import RidersPage from "./pages/RidersPage/RidersPage";
import GetInTouch from "./pages/GetInTouch/Index";
import Dietitian from "./pages/Dietitian/Index";
import Contact from "./pages/Contact/Index";
import AgentRegistration from "./pages/AgentRegistration";
import AgentLogin from "./pages/AgentLogin";
import AgentDashboard from "./pages/AgentDashboard";
import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";
import Referrals from "./pages/AgentDashboard/Referrals";
import Settings from "./pages/AgentDashboard/Settings";
import { AuthProvider } from "./contexts/AuthContext";
import ProtectedRoute from "./components/ProtectedRoute.tsx";
import Faq from "./pages/Faq/Index";
import AOS from "aos";
import "aos/dist/aos.css";
import { useEffect } from "react";
import Footer from "./components/Footer.jsx";
import Header from "./components/Header.jsx";

function App() {
  useEffect(() => {
    AOS.init();
  });

  return (
   <AuthProvider>
    <Routes>
      <Route path="/" element={<LandingPage />} />
      <Route path="become-a-rider" element={<RidersPage />} />
      <Route path="get-in-touch" element={<GetInTouch />} />
      <Route path="faq" element={<Faq />} />
      <Route path="dietitian" element={<Dietitian />} />
      <Route path="contact-us" element={<Contact />} />

      {/* Agent routes without header and footer */}
        <Route path="/agent-registration" element={
          <>
          <Header />
          <AgentRegistration />
          <Footer />
       
        </>
        }
        />
        <Route path="/agent-login" element={
           <>
          <Header />
          <AgentLogin />
           <Footer />
          
           </>
          } />
        <Route path="/forgot-password" element={
           <>
          <Header />
          <ForgotPassword />
           <Footer />
          
           </>
          } />
        <Route path="/reset-password" element={
           <>
          <Header />
          <ResetPassword />
          <Footer />
          
           </>
          } />

        {/* Protected dashboard routes */}
        <Route
          path="/agent-dashboard"
          element={
            <ProtectedRoute>
              <AgentDashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="/agent-dashboard/referrals"
          element={
            <ProtectedRoute>
              <Referrals />
            </ProtectedRoute>
          }
        />
        <Route
          path="/agent-dashboard/settings"
          element={
            <ProtectedRoute>
              <Settings />
            </ProtectedRoute>
          }
        />
    </Routes>
    </AuthProvider>
  );
}

export default App;
