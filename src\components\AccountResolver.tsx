import React, { useState, useEffect, useRef } from 'react';
import { Bank } from '../types/bank';
import { bankService } from '../services/bankService';

interface AccountResolverProps {
  accountNumber: string;
  selectedBank: Bank | null;
  onAccountNumberChange: (value: string) => void;
  onAccountResolved: (accountName: string) => void;
  disabled?: boolean;
  error?: string;
  className?: string;
}

const AccountResolver: React.FC<AccountResolverProps> = ({
  accountNumber,
  selectedBank,
  onAccountNumberChange,
  onAccountResolved,
  disabled = false,
  error,
  className = "",
}) => {
  const [isResolving, setIsResolving] = useState(false);
  const [resolveError, setResolveError] = useState<string>("");
  const [resolvedAccountName, setResolvedAccountName] = useState<string>("");
  const [isResolved, setIsResolved] = useState(false);
  const previousAccountNumber = useRef<string>("");
  const previousBankCode = useRef<string>("");

  // Reset resolved state when account number or bank changes
  useEffect(() => {
    const currentAccountNumber = accountNumber;
    const currentBankCode = selectedBank?.bank_code || "";

    // Only reset if account number or bank actually changed
    if (currentAccountNumber !== previousAccountNumber.current ||
        currentBankCode !== previousBankCode.current) {

      // Clear resolved state
      setIsResolved(false);
      setResolvedAccountName("");
      setResolveError("");

      // Update refs
      previousAccountNumber.current = currentAccountNumber;
      previousBankCode.current = currentBankCode;
    }
  }, [accountNumber, selectedBank]);

  const handleAccountNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ""); // Only allow digits
    onAccountNumberChange(value);
  };

  const resolveAccount = async () => {
    if (!selectedBank || !accountNumber) {
      setResolveError("Please select a bank and enter account number");
      return;
    }

    // Validate account number format first
    const validationError = bankService.validateAccountNumber(accountNumber);
    if (validationError) {
      setResolveError(validationError);
      return;
    }

    setIsResolving(true);
    setResolveError("");

    try {
      const result = await bankService.resolveAccount(accountNumber, selectedBank.bank_code);

      if (result.success && result.data) {
        const accountName = result.data.account_name;
        setResolvedAccountName(accountName);
        setIsResolved(true);
        onAccountResolved(accountName);
      } else {
        setResolveError(result.message || "Failed to resolve account");
        setIsResolved(false);
        onAccountResolved("");
      }
    } catch (err) {
      setResolveError("Failed to resolve account. Please try again.");
      setIsResolved(false);
      onAccountResolved("");
    } finally {
      setIsResolving(false);
    }
  };

  const canResolve = selectedBank && accountNumber.length >= 10 && !isResolving && !isResolved;

  return (
    <div className={className}>
      <label className="block text-gray-700 font-medium mb-2">
        Account Number
      </label>

      <div className="space-y-3">
        <div className="flex gap-2">
          <input
            type="text"
            value={accountNumber}
            onChange={handleAccountNumberChange}
            disabled={disabled}
            placeholder="Enter 10-digit account number"
            maxLength={12}
            className={`flex-1 px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 disabled:bg-gray-50 ${
              error || resolveError ? "border-red-500" : "border-gray-300"
            }`}
          />

          <button
            type="button"
            onClick={resolveAccount}
            disabled={!canResolve || disabled}
            className={`px-4 py-3 rounded-lg font-medium transition-colors ${
              canResolve && !disabled
                ? "bg-green-600 hover:bg-green-700 text-white"
                : "bg-gray-300 text-gray-500 cursor-not-allowed"
            }`}
          >
            {isResolving ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Verifying...</span>
              </div>
            ) : (
              "Verify"
            )}
          </button>
        </div>

        {/* Account Name Display */}
        {isResolved && resolvedAccountName && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2">
              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <div>
                <p className="text-sm font-medium text-green-800">Account Verified</p>
                <p className="text-sm text-green-700">{resolvedAccountName}</p>
              </div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {(error || resolveError) && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2">
              <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-sm text-red-700">{error || resolveError}</p>
            </div>
          </div>
        )}
      </div>

      <div className="mt-1 space-y-1">
        <p className="text-xs text-gray-500">
          Enter your 10-12 digit bank account number
        </p>
        {selectedBank && (
          <p className="text-xs text-gray-500">
            Bank: {selectedBank.bank_name} (Code: {selectedBank.bank_code})
          </p>
        )}
        {!selectedBank && (
          <p className="text-xs text-orange-600">
            Please select a bank first to verify your account
          </p>
        )}
      </div>
    </div>
  );
};

export default AccountResolver;
