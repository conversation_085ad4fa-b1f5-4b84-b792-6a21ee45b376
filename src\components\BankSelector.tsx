import React, { useState, useEffect } from 'react';
import { Bank } from '../types/bank';
import { bankService } from '../services/bankService';

interface BankSelectorProps {
  selectedBank: Bank | null;
  onBankSelect: (bank: Bank | null) => void;
  disabled?: boolean;
  error?: string;
  placeholder?: string;
  className?: string;
}

const BankSelector: React.FC<BankSelectorProps> = ({
  selectedBank,
  onBankSelect,
  disabled = false,
  error,
  placeholder = "Select a bank",
  className = "",
}) => {
  const [banks, setBanks] = useState<Bank[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadError, setLoadError] = useState<string>("");
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    loadBanks();
  }, []);

  const loadBanks = async () => {
    setIsLoading(true);
    setLoadError("");

    try {
      const result = await bankService.getAllBanks();
      
      if (result.success && result.data) {
        setBanks(result.data);
      } else {
        setLoadError(result.message);
      }
    } catch (err) {
      setLoadError("Failed to load banks. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const filteredBanks = banks.filter(bank =>
    bank.bank_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleBankSelect = (bank: Bank) => {
    onBankSelect(bank);
    setIsOpen(false);
    setSearchTerm("");
  };

  const handleClear = () => {
    onBankSelect(null);
    setSearchTerm("");
  };

  return (
    <div className={`relative ${className}`}>
      <label className="block text-gray-700 font-medium mb-2">
        Bank Name
      </label>
      
      <div className="relative">
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled || isLoading}
          className={`w-full px-4 py-3 border rounded-lg text-left focus:outline-none focus:ring-2 focus:ring-green-500 disabled:bg-gray-50 disabled:cursor-not-allowed ${
            error ? "border-red-500" : "border-gray-300"
          } ${isOpen ? "ring-2 ring-green-500" : ""}`}
        >
          {isLoading ? (
            <span className="text-gray-500">Loading banks...</span>
          ) : selectedBank ? (
            <span className="text-gray-900">{selectedBank.bank_name}</span>
          ) : (
            <span className="text-gray-500">{placeholder}</span>
          )}
          
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            {selectedBank && !disabled && (
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClear();
                }}
                className="mr-2 text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            )}
            <svg
              className={`w-5 h-5 text-gray-400 transition-transform ${
                isOpen ? "rotate-180" : ""
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </button>

        {isOpen && !disabled && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-hidden">
            <div className="p-3 border-b border-gray-200">
              <input
                type="text"
                placeholder="Search banks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
                autoFocus
              />
            </div>
            
            <div className="max-h-48 overflow-y-auto">
              {filteredBanks.length > 0 ? (
                filteredBanks.map((bank) => (
                  <button
                    key={bank._id}
                    type="button"
                    onClick={() => handleBankSelect(bank)}
                    className="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
                  >
                    <div className="font-medium text-gray-900">
                      {bank.bank_name}
                    </div>
                    <div className="text-sm text-gray-500">
                      Code: {bank.bank_code}
                    </div>
                  </button>
                ))
              ) : (
                <div className="px-4 py-3 text-gray-500 text-center">
                  {searchTerm ? "No banks found" : "No banks available"}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {(error || loadError) && (
        <p className="mt-1 text-sm text-red-600">
          {error || loadError}
        </p>
      )}

      {loadError && (
        <button
          type="button"
          onClick={loadBanks}
          className="mt-2 text-sm text-green-600 hover:text-green-700 underline"
        >
          Retry loading banks
        </button>
      )}

      <p className="mt-1 text-xs text-gray-500">
        Select your bank from the list
      </p>
    </div>
  );
};

export default BankSelector;
