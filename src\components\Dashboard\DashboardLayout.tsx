/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from "react";
import { NavLink, useNavigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { agentApiService } from "../../services/agentApiService";
import { getCategoryDisplayName } from "../../utils/categoryUtils";
import {
  FaBars,
  FaTimes,
  FaTachometerAlt,
  FaUsers,
  FaCog,
  FaSignOutAlt,
} from "react-icons/fa";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { state, logout } = useAuth();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [dashboardData, setDashboardData] = useState<any>(null);

  // Debug logging for category
  useEffect(() => {
    if (state.agent) {
      console.log("🔍 DashboardLayout - Agent data:", state.agent);
      console.log(
        "📋 DashboardLayout - Category:",
        state.agent.category || "NOT FOUND"
      );
    }
  }, [state.agent]);

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  // Fetch dashboard data to get the real referral code
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!state.agent) return;

      try {
        const response = await agentApiService.getDashboardData(state.agent.id);
        if (response.success && response.data) {
          setDashboardData(response.data);
        }
      } catch (error) {
        console.error("Error fetching dashboard data for sidebar:", error);
      }
    };

    fetchDashboardData();
  }, [state.agent]);

  const menuItems = [
    { to: "/agent-dashboard", icon: FaTachometerAlt, label: "Dashboard" },
    { to: "/agent-dashboard/referrals", icon: FaUsers, label: "Referrals" },
    { to: "/agent-dashboard/settings", icon: FaCog, label: "Settings" },
  ];

  return (
    <div className="min-h-screen bg-gray-50 font-poppins flex">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        } transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col`}
      >
        <div className="flex items-center justify-between h-16 px-6 border-b">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">FB</span>
            </div>
            <span className="text-xl font-bold text-gray-800">FOOD BANK</span>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-gray-500 hover:text-gray-700"
          >
            <FaTimes />
          </button>
        </div>

        {/* User Profile Section */}
        <div className="p-6 border-b">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 font-semibold text-lg">
                {state.agent?.firstname?.charAt(0)}
                {state.agent?.lastname?.charAt(0)}
              </span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">
                {dashboardData?.name ||
                  `${state.agent?.firstname || ""} ${
                    state.agent?.lastname || ""
                  }`.trim()}
              </h3>
            </div>
          </div>
          <div className="mt-4 space-y-1 text-sm text-gray-600">
            <p>
              <strong>Category:</strong>{" "}
              {getCategoryDisplayName(state.agent?.category)}
            </p>
            <p>
              <strong>Contact:</strong>{" "}
              {state.agent?.contactNumber || "Not specified"}
            </p>
            <p>
              <strong>Referral Code:</strong>{" "}
              {dashboardData?.referralCode ||
                state.agent?.referralCode ||
                "Loading..."}
            </p>
          </div>
        </div>

        {/* Menu */}
        <nav className="flex-1 px-4 py-6">
          <div className="mb-4">
            <h4 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
              MENU
            </h4>
            <ul className="space-y-2">
              {menuItems.map((item) => (
                <li key={item.to}>
                  <NavLink
                    to={item.to}
                    end={item.to === "/agent-dashboard"}
                    className={({ isActive }) =>
                      `flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                        isActive
                          ? "bg-green-100 text-green-600"
                          : "text-gray-600 hover:bg-gray-100 hover:text-gray-800"
                      }`
                    }
                    onClick={() => setSidebarOpen(false)}
                  >
                    <item.icon className="w-4 h-4" />
                    {item.label}
                  </NavLink>
                </li>
              ))}
            </ul>
          </div>
        </nav>

        {/* Logout Button */}
        <div className="p-4 border-t">
          <button
            onClick={handleLogout}
            className="flex items-center gap-3 w-full px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 rounded-lg transition-colors"
          >
            <FaSignOutAlt className="w-4 h-4" />
            Logout
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Header */}
        <header className="bg-white shadow-sm border-b h-16 flex items-center justify-between px-6">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden text-gray-500 hover:text-gray-700"
            >
              <FaBars />
            </button>

            {/* Search Bar */}
            {/* <div className="hidden md:flex items-center bg-gray-100 rounded-lg px-3 py-2 w-80">
              <FaSearch className="text-gray-400 mr-2" />
              <input
                type="text"
                placeholder="Search your route..."
                className="bg-transparent outline-none text-sm w-full"
              />
            </div> */}
          </div>

          {/* User Info */}
          <div className="flex items-center gap-3">
            <div className="text-right hidden sm:block">
              <p className="text-sm font-semibold text-gray-800">
                {dashboardData?.name ||
                  `${state.agent?.firstname || ""} ${
                    state.agent?.lastname || ""
                  }`.trim()}
              </p>
              <p className="text-xs text-gray-500">
                {getCategoryDisplayName(state.agent?.category)}
              </p>
            </div>
            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 font-semibold">
                {state.agent?.firstname?.charAt(0)}
                {state.agent?.lastname?.charAt(0)}
              </span>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 p-6 overflow-auto">{children}</main>
      </div>
    </div>
  );
};

export default DashboardLayout;
