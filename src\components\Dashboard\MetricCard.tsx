import React from "react";

interface MetricCardProps {
  title: string;
  value: string;
  icon: string;
  color: "green" | "orange" | "red" | "blue" | "purple" | "indigo" | "yellow";
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  color,
}) => {
  const colorClasses = {
    green: "border-l-green-500 bg-green-50",
    orange: "border-l-orange-500 bg-orange-50",
    red: "border-l-red-500 bg-red-50",
    blue: "border-l-blue-500 bg-blue-50",
    purple: "border-l-purple-500 bg-purple-50",
    indigo: "border-l-indigo-500 bg-indigo-50",
    yellow: "border-l-yellow-500 bg-yellow-50",
  };

  const iconBgClasses = {
    green: "bg-green-100",
    orange: "bg-orange-100",
    red: "bg-red-100",
    blue: "bg-blue-100",
    purple: "bg-purple-100",
    indigo: "bg-indigo-100",
    yellow: "bg-yellow-100",
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border-l-4 ${colorClasses[color]} p-6`}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-800">{value}</p>
        </div>
        <div
          className={`w-12 h-12 rounded-full ${iconBgClasses[color]} flex items-center justify-center text-2xl`}
        >
          {icon}
        </div>
      </div>
    </div>
  );
};

export default MetricCard;
