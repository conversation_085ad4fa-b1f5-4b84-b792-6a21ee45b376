/// <reference types="vite/client" />

// Environment configuration
export const config = {
  // API Configuration
  apiBaseUrl:
    import.meta.env.REACT_APP_API_URL ||
    "https://foodbank-backend-x3k8.onrender.com/v1/api",

  // Environment
  nodeEnv: import.meta.env.VITE_NODE_ENV || "development",

  // API Settings
  apiTimeout: parseInt(import.meta.env.VITE_API_TIMEOUT || "30000"),

  // Development flags
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,

  // Validation
  validate() {
    const requiredVars = ["REACT_APP_API_URL"];
    const missing = requiredVars.filter((varName) => !import.meta.env[varName]);

    if (missing.length > 0) {
      console.warn(`Missing environment variables: ${missing.join(", ")}`);
      console.warn("Using fallback values. Please check your .env file.");
    }

    return missing.length === 0;
  },
};

// Validate configuration on import
config.validate();

// Log configuration in development
if (config.isDevelopment) {
  console.log("🔧 Environment Configuration:", {
    apiBaseUrl: config.apiBaseUrl,
    nodeEnv: config.nodeEnv,
    apiTimeout: config.apiTimeout,
    isDevelopment: config.isDevelopment,
    isProduction: config.isProduction,
  });
}
