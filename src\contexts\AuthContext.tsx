/* eslint-disable react-refresh/only-export-components */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { createContext, useContext, useReducer, useEffect } from "react";
import {
  Agent,
  AuthState,
  LoginCredentials,
  AgentRegistrationData,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  UpdateProfileRequest,
  ChangePasswordRequest,
  UpdateAccountInfoRequest,
} from "../types/agent";
import { agentApiService } from "../services/agentApiService";

interface AuthContextType {
  state: AuthState;
  login: (
    credentials: LoginCredentials
  ) => Promise<{ success: boolean; message: string }>;
  register: (
    data: AgentRegistrationData
  ) => Promise<{ success: boolean; message: string }>;
  forgotPassword: (
    data: ForgotPasswordRequest
  ) => Promise<{ success: boolean; message: string }>;
  resetPassword: (
    data: ResetPasswordRequest
  ) => Promise<{ success: boolean; message: string }>;
  logout: () => void;
  updateProfile: (
    data: UpdateProfileRequest
  ) => Promise<{ success: boolean; message: string }>;
  changePassword: (
    data: ChangePasswordRequest
  ) => Promise<{ success: boolean; message: string }>;
  updateAccountInfo: (
    data: UpdateAccountInfoRequest
  ) => Promise<{ success: boolean; message: string }>;
  updateAgentData: (updates: Partial<Agent>) => void;
   refreshAgentData: () => Promise<void>;
  isAuthenticated: () => boolean;
}

type AuthAction =
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "LOGIN_SUCCESS"; payload: Agent }
  | { type: "LOGIN_FAILURE" }
  | { type: "LOGOUT" }
  | { type: "UPDATE_PROFILE"; payload: Agent };

const initialState: AuthState = {
  isAuthenticated: false,
  agent: null,
  loading: false,
};

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case "SET_LOADING":
      return { ...state, loading: action.payload };
    case "LOGIN_SUCCESS":
      return {
        ...state,
        isAuthenticated: true,
        agent: action.payload,
        loading: false,
      };
    case "LOGIN_FAILURE":
      return {
        ...state,
        isAuthenticated: false,
        agent: null,
        loading: false,
      };
    case "LOGOUT":
      return {
        ...state,
        isAuthenticated: false,
        agent: null,
        loading: false,
      };
    case "UPDATE_PROFILE":
      return {
        ...state,
        agent: action.payload,
      };
    default:
      return state;
  }
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for stored authentication on mount
  useEffect(() => {
    const storedAgent = localStorage.getItem("agent");
    const isTokenValid = agentApiService.isAuthenticated();

    if (storedAgent && isTokenValid) {
      try {
        const agent = JSON.parse(storedAgent);
        dispatch({ type: "LOGIN_SUCCESS", payload: agent });
      } catch (error) {
        localStorage.removeItem("agent");
        agentApiService.logout();
      }
    } else {
      // Clear invalid data
      localStorage.removeItem("agent");
      agentApiService.logout();
    }
  }, []);

  const login = async (credentials: LoginCredentials) => {
    dispatch({ type: "SET_LOADING", payload: true });

    try {
      const result = await agentApiService.loginAgent(credentials);

      if (result.success && result.agent) {
        // Debug logging for category

        localStorage.setItem("agent", JSON.stringify(result.agent));
        dispatch({ type: "LOGIN_SUCCESS", payload: result.agent });
      } else {
        dispatch({ type: "LOGIN_FAILURE" });
      }

      return result;
    } catch (error) {
      dispatch({ type: "LOGIN_FAILURE" });
      return { success: false, message: "Login failed. Please try again." };
    }
  };

  const register = async (data: AgentRegistrationData) => {
    dispatch({ type: "SET_LOADING", payload: true });

    try {
      const result = await agentApiService.registerAgent(data);
      dispatch({ type: "SET_LOADING", payload: false });
      return result;
    } catch (error) {
      dispatch({ type: "SET_LOADING", payload: false });
      return {
        success: false,
        message: "Registration failed. Please try again.",
      };
    }
  };

  const forgotPassword = async (data: ForgotPasswordRequest) => {
    dispatch({ type: "SET_LOADING", payload: true });

    try {
      const result = await agentApiService.forgotPassword(data);
      dispatch({ type: "SET_LOADING", payload: false });
      return result;
    } catch (error) {
      dispatch({ type: "SET_LOADING", payload: false });
      return {
        success: false,
        message: "Failed to send reset email. Please try again.",
      };
    }
  };

  const resetPassword = async (data: ResetPasswordRequest) => {
    dispatch({ type: "SET_LOADING", payload: true });

    try {
      const result = await agentApiService.resetPassword(data);
      dispatch({ type: "SET_LOADING", payload: false });
      return result;
    } catch (error) {
      dispatch({ type: "SET_LOADING", payload: false });
      return {
        success: false,
        message: "Password reset failed. Please try again.",
      };
    }
  };

  const logout = () => {
    localStorage.removeItem("agent");
    agentApiService.logout(); // Clear the token
    dispatch({ type: "LOGOUT" });
  };

  const isAuthenticated = () => {
    return agentApiService.isAuthenticated();
  };

  const updateAgentData = (updates: Partial<Agent>) => {
    if (state.agent) {
      const updatedAgent = { ...state.agent, ...updates };
      localStorage.setItem("agent", JSON.stringify(updatedAgent));
      dispatch({ type: "UPDATE_PROFILE", payload: updatedAgent });
    }
  };

  // Refresh agent data from backend
  const refreshAgentData = async () => {
    if (!state.agent) return;

    try {
      const result = await agentApiService.getAgentById(state.agent.id);
      if (result.success && result.data) {
        localStorage.setItem("agent", JSON.stringify(result.data));
        dispatch({ type: "UPDATE_PROFILE", payload: result.data });
      }
    } catch (error) {
      console.error("Failed to refresh agent data:", error);
    }
  };

  const updateProfile = async (data: UpdateProfileRequest) => {
    if (!state.agent) {
      return { success: false, message: "No agent logged in" };
    }

    try {
      const result = await agentApiService.updateProfile(state.agent.id, data);
       // If API call successful, refresh agent data from backend
      if (result.success) {
        await refreshAgentData();
      }

      return result;
    } catch (error) {
      return {
        success: false,
        message: "Profile update failed. Please try again.",
      };
    }
  };

  const changePassword = async (data: ChangePasswordRequest) => {
    if (!state.agent) {
      return { success: false, message: "No agent logged in" };
    }

    try {
      const result = await agentApiService.changePassword(state.agent.id, data);
      return result;
    } catch (error) {
      return {
        success: false,
        message: "Password change failed. Please try again.",
      };
    }
  };

  const updateAccountInfo = async (data: UpdateAccountInfoRequest) => {
    if (!state.agent) {
      return { success: false, message: "No agent logged in" };
    }

    try {
      const result = await agentApiService.updateAccountInfo(
        state.agent.id,
        data
      );
         // If API call successful, refresh agent data from backend
      if (result.success) {
        await refreshAgentData();
      }
      return result;
    } catch (error) {
      return {
        success: false,
        message: "Account info update failed. Please try again.",
      };
    }
  };

  const value: AuthContextType = {
    state,
    login,
    register,
    forgotPassword,
    resetPassword,
    logout,
    updateProfile,
    changePassword,
    updateAccountInfo,
    updateAgentData,
     refreshAgentData,
    isAuthenticated,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
