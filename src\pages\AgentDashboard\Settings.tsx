/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "../../contexts/AuthContext";
import DashboardLayout from "../../components/Dashboard/DashboardLayout";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { getCategoryDisplayName } from "../../utils/categoryUtils";
import BankSelector from "../../components/BankSelector";
import AccountResolver from "../../components/AccountResolver";
import { Bank } from "../../types/bank";
import { validateBankAccountInfo } from "../../utils/validation";

const Settings = () => {
  const {
    state,
    updateProfile,
    changePassword,
    updateAccountInfo,
    updateAgentData,
  } = useAuth();

  // Profile editing states
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [profileMessage, setProfileMessage] = useState("");
  const [isProfileSuccess, setIsProfileSuccess] = useState(false);
  const [isProfileLoading, setIsProfileLoading] = useState(false);

  // Password change states
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [passwordMessage, setPasswordMessage] = useState("");
  const [isPasswordSuccess, setIsPasswordSuccess] = useState(false);
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);

  // Account info states
  const [isEditingAccount, setIsEditingAccount] = useState(false);
  const [accountMessage, setAccountMessage] = useState("");
  const [isAccountSuccess, setIsAccountSuccess] = useState(false);
  const [isAccountLoading, setIsAccountLoading] = useState(false);

  // Form data for profile (only phone number is editable)
  const [profileData, setProfileData] = useState({
    phoneNumber: state.agent?.contactNumber || "",
  });

  // Form data for password change
  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  // Form data for account info
  const [accountData, setAccountData] = useState({
    accountNumber: state.agent?.accountNumber || "",
    bankName: state.agent?.bankName || "",
    bankCode: state.agent?.bankCode || "",
    accountName: state.agent?.accountName || "",
    bankId: state.agent?.bankId || "",
  });

  // Bank-related states
  const [selectedBank, setSelectedBank] = useState<Bank | null>(null);
  const [resolvedAccountName, setResolvedAccountName] = useState<string>("");
  const [bankErrors, setBankErrors] = useState<{ [key: string]: string }>({});

  // Update form data when agent data changes
  useEffect(() => {
    if (state.agent) {
      setProfileData({
        phoneNumber: state.agent.contactNumber || "",
      });
      setAccountData({
        accountNumber: state.agent.accountNumber || "",
        bankName: state.agent.bankName || "",
        bankCode: state.agent.bankCode || "",
        accountName: state.agent.accountName || "",
        bankId: state.agent.bankId || "",
      });

      // Set resolved account name if available
      if (state.agent.accountName) {
        setResolvedAccountName(state.agent.accountName);
      }

      // Set selected bank if bank data exists
      if (state.agent.bankCode && state.agent.bankName) {
        setSelectedBank({
          _id: state.agent.bankId || "",
          bank_name: state.agent.bankName,
          bank_code: state.agent.bankCode,
          createdAt: "",
          updatedAt: "",
        });
      }
    }
  }, [state.agent]);

  // Validation functions
  const validatePhoneNumber = (phone: string): string | null => {
    if (!phone.trim()) {
      return "Phone number is required";
    }

    // Remove all non-digit characters for validation
    const cleanPhone = phone.replace(/\D/g, "");

    // Check if it's a valid Nigerian phone number (11 digits starting with 0, or 10 digits without 0)
    if (cleanPhone.length === 11 && cleanPhone.startsWith("0")) {
      return null; // Valid Nigerian number with leading 0
    } else if (cleanPhone.length === 10 && !cleanPhone.startsWith("0")) {
      return null; // Valid Nigerian number without leading 0
    } else if (cleanPhone.length >= 10 && cleanPhone.length <= 15) {
      return null; // Valid international number
    }

    return "Please enter a valid phone number (e.g., *********** or +*************)";
  };



  // Profile handlers
  const handleProfileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileData((prev) => ({ ...prev, [name]: value }));

    // Clear error message when user starts typing
    if (profileMessage) {
      setProfileMessage("");
    }
  };

  const handleProfileSave = async () => {
    // Validate phone number
    const phoneError = validatePhoneNumber(profileData.phoneNumber);
    if (phoneError) {
      setIsProfileSuccess(false);
      setProfileMessage(phoneError);
      return;
    }

    setIsProfileLoading(true);
    setProfileMessage("");

    try {
      const result = await updateProfile(profileData);

      if (result.success) {
        setIsProfileSuccess(true);
        setProfileMessage("Profile updated successfully!");
        setIsEditingProfile(false);

        // Update the agent data to reflect the change
        updateAgentData({
          contactNumber: profileData.phoneNumber,
        });
      } else {
        setIsProfileSuccess(false);
        setProfileMessage(result.message);
      }
    } catch (e) {
      setIsProfileSuccess(false);
      setProfileMessage("Failed to update profile. Please try again.");
    } finally {
      setIsProfileLoading(false);
    }
  };

  // Password handlers
  const handlePasswordInputChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    setPasswordData((prev) => ({ ...prev, [name]: value }));

    // Clear error message when user starts typing
    if (passwordMessage) {
      setPasswordMessage("");
    }
  };

  const handlePasswordChange = async () => {
    // Validation
    if (!passwordData.oldPassword.trim()) {
      setIsPasswordSuccess(false);
      setPasswordMessage("Current password is required.");
      return;
    }

    if (!passwordData.newPassword.trim()) {
      setIsPasswordSuccess(false);
      setPasswordMessage("New password is required.");
      return;
    }

    if (passwordData.newPassword.length < 6) {
      setIsPasswordSuccess(false);
      setPasswordMessage("New password must be at least 6 characters long.");
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setIsPasswordSuccess(false);
      setPasswordMessage("New passwords do not match.");
      return;
    }

    if (passwordData.oldPassword === passwordData.newPassword) {
      setIsPasswordSuccess(false);
      setPasswordMessage(
        "New password must be different from current password."
      );
      return;
    }

    setIsPasswordLoading(true);
    setPasswordMessage("");

    try {
      const result = await changePassword({
        oldPassword: passwordData.oldPassword,
        newPassword: passwordData.newPassword,
      });

      if (result.success) {
        setIsPasswordSuccess(true);
        setPasswordMessage("Password changed successfully!");
        setIsChangingPassword(false);
        setPasswordData({
          oldPassword: "",
          newPassword: "",
          confirmPassword: "",
        });
      } else {
        setIsPasswordSuccess(false);
        setPasswordMessage(result.message);
      }
    } catch (e) {
      setIsPasswordSuccess(false);
      setPasswordMessage("Failed to change password. Please try again.");
    } finally {
      setIsPasswordLoading(false);
    }
  };

  // Enhanced account info handlers
  const handleBankSelect = useCallback((bank: Bank | null) => {
    setSelectedBank(bank);
    setBankErrors({});
    setAccountMessage("");

    if (bank) {
      setAccountData(prev => ({
        ...prev,
        bankName: bank.bank_name,
        bankCode: bank.bank_code,
        bankId: bank._id,
      }));
    } else {
      setAccountData(prev => ({
        ...prev,
        bankName: "",
        bankCode: "",
        bankId: "",
      }));
    }
  }, []);

  const handleAccountNumberChange = useCallback((value: string) => {
    setAccountData(prev => ({ ...prev, accountNumber: value }));
    setBankErrors({});
    setAccountMessage("");
  }, []);

  const handleAccountResolved = useCallback((accountName: string) => {
    setResolvedAccountName(accountName);
    setAccountData(prev => ({ ...prev, accountName }));
    setBankErrors({});
    setAccountMessage("");
  }, []);

  const handleAccountSave = async () => {
    // Validate all account information
    if (!selectedBank) {
      setIsAccountSuccess(false);
      setAccountMessage("Please select a bank");
      return;
    }

    if (!accountData.accountNumber) {
      setIsAccountSuccess(false);
      setAccountMessage("Please enter account number");
      return;
    }

    if (!resolvedAccountName) {
      setIsAccountSuccess(false);
      setAccountMessage("Please verify your account number first");
      return;
    }

    // Validate using enhanced validation
    const errors = validateBankAccountInfo(
      accountData.accountNumber,
      accountData.bankCode,
      accountData.bankName,
      accountData.accountName
    );

    if (Object.keys(errors).length > 0) {
      setBankErrors(errors);
      setIsAccountSuccess(false);
      setAccountMessage("Please fix the validation errors");
      return;
    }

    setIsAccountLoading(true);
    setAccountMessage("");
    setBankErrors({});

    try {
      const result = await updateAccountInfo({
        accountNumber: accountData.accountNumber,
        bankName: accountData.bankName,
        bankCode: accountData.bankCode,
        accountName: accountData.accountName,
        bankId: accountData.bankId,
      });

      if (result.success) {
        setIsAccountSuccess(true);
        setAccountMessage("Account information updated successfully!");
        setIsEditingAccount(false);

        // Update the agent data to reflect all changes
        updateAgentData({
          accountNumber: accountData.accountNumber,
          bankName: accountData.bankName,
          bankCode: accountData.bankCode,
          accountName: accountData.accountName,
          bankId: accountData.bankId,
        });
      } else {
        setIsAccountSuccess(false);
        setAccountMessage(result.message);
      }
    } catch (e) {
      setIsAccountSuccess(false);
      setAccountMessage(
        "Failed to update account information. Please try again."
      );
    } finally {
      setIsAccountLoading(false);
    }
  };

  const handleAccountCancel = () => {
    setIsEditingAccount(false);
    setAccountData({
      accountNumber: state.agent?.accountNumber || "",
      bankName: state.agent?.bankName || "",
      bankCode: state.agent?.bankCode || "",
      accountName: state.agent?.accountName || "",
      bankId: state.agent?.bankId || "",
    });
    setSelectedBank(null);
    setResolvedAccountName(state.agent?.accountName || "");
    setAccountMessage("");
    setBankErrors({});
  };

  // const handleCancel = () => {
  //   setFormData({
  //     firstName: state.agent?.firstName || "",
  //     surname: state.agent?.surname || "",
  //     email: state.agent?.email || "",
  //     contactNumber: state.agent?.contactNumber || "+1 (936) 514-1641",
  //     accountNumber: state.agent?.accountNumber || "*********",
  //     bankName: state.agent?.bankName || "Zenith Bank",
  //   });
  //   setIsEditing(false);
  //   setMessage("");
  // };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-800">Settings</h1>

        {/* Basic Information Section (Read-only) */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-800">
              Basic Information
            </h2>
            <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
              Read Only
            </span>
          </div>

          <div className="flex items-center gap-4 mb-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 font-bold text-xl">
                {state.agent?.firstname?.charAt(0)}
                {state.agent?.lastname?.charAt(0)}
              </span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">
                {state.agent?.firstname} {state.agent?.lastname}
              </h3>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                First Name
              </label>
              <input
                type="text"
                value={state.agent?.firstname || ""}
                disabled
                className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Last Name
              </label>
              <input
                type="text"
                value={state.agent?.lastname || ""}
                disabled
                className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Email
              </label>
              <input
                type="email"
                value={state.agent?.email || ""}
                disabled
                className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Category
              </label>
              <input
                type="text"
                value={getCategoryDisplayName(state.agent?.category)}
                disabled
                className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
              />
            </div>
          </div>
        </div>

        {/* Profile Section (Phone Number) */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-800">
              Contact Information
            </h2>
            {!isEditingProfile ? (
              <button
                onClick={() => setIsEditingProfile(true)}
                className="text-green-600 hover:text-green-700 text-sm font-medium"
              >
                Edit
              </button>
            ) : (
              <div className="flex gap-2">
                <button
                  onClick={handleProfileSave}
                  disabled={isProfileLoading}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-500 disabled:cursor-not-allowed text-white text-sm font-medium rounded-lg transition-colors flex items-center gap-2"
                >
                  {isProfileLoading && (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  )}
                  {isProfileLoading ? "Saving..." : "Save"}
                </button>
                <button
                  onClick={() => {
                    setIsEditingProfile(false);
                    setProfileData({
                      phoneNumber: state.agent?.contactNumber || "",
                    });
                    setProfileMessage("");
                  }}
                  className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            )}
          </div>

          {profileMessage && (
            <div
              className={`mb-4 p-4 rounded-lg ${
                isProfileSuccess
                  ? "bg-green-100 text-green-700 border border-green-300"
                  : "bg-red-100 text-red-700 border border-red-300"
              }`}
            >
              {profileMessage}
            </div>
          )}

          <div>
            <label className="block text-gray-700 font-medium mb-2">
              Phone Number
            </label>
            <input
              type="text"
              name="phoneNumber"
              value={profileData.phoneNumber}
              onChange={handleProfileInputChange}
              disabled={!isEditingProfile}
              placeholder="Enter your phone number (e.g., ***********)"
              className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 disabled:bg-gray-50 ${
                !isProfileSuccess && profileMessage && !isEditingProfile
                  ? "border-red-500"
                  : "border-gray-300"
              }`}
            />
            {!isProfileSuccess && profileMessage && isEditingProfile && (
              <p className="mt-1 text-sm text-red-600">{profileMessage}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Enter a valid Nigerian phone number (e.g., ***********)
            </p>
          </div>
        </div>

        {/* Password Change Section */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-800">
              Change Password
            </h2>
            {!isChangingPassword ? (
              <button
                onClick={() => setIsChangingPassword(true)}
                className="text-green-600 hover:text-green-700 text-sm font-medium"
              >
                Change Password
              </button>
            ) : (
              <div className="flex gap-2">
                <button
                  onClick={handlePasswordChange}
                  disabled={isPasswordLoading}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white text-sm font-medium rounded-lg transition-colors flex items-center gap-2"
                >
                  {isPasswordLoading && (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  )}
                  {isPasswordLoading ? "Updating..." : "Update Password"}
                </button>
                <button
                  onClick={() => {
                    setIsChangingPassword(false);
                    setPasswordData({
                      oldPassword: "",
                      newPassword: "",
                      confirmPassword: "",
                    });
                    setPasswordMessage("");
                  }}
                  className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            )}
          </div>

          {passwordMessage && (
            <div
              className={`mb-4 p-4 rounded-lg ${
                isPasswordSuccess
                  ? "bg-green-100 text-green-700 border border-green-300"
                  : "bg-red-100 text-red-700 border border-red-300"
              }`}
            >
              {passwordMessage}
            </div>
          )}

          {isChangingPassword && (
            <div className="space-y-4">
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Current Password
                </label>
                <div className="relative">
                  <input
                    type={showOldPassword ? "text" : "password"}
                    name="oldPassword"
                    value={passwordData.oldPassword}
                    onChange={handlePasswordInputChange}
                    placeholder="Enter current password"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 pr-12"
                  />
                  <button
                    type="button"
                    onClick={() => setShowOldPassword(!showOldPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    {showOldPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  New Password
                </label>
                <div className="relative">
                  <input
                    type={showNewPassword ? "text" : "password"}
                    name="newPassword"
                    value={passwordData.newPassword}
                    onChange={handlePasswordInputChange}
                    placeholder="Enter new password"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 pr-12"
                  />
                  <button
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    {showNewPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Confirm New Password
                </label>
                <input
                  type="password"
                  name="confirmPassword"
                  value={passwordData.confirmPassword}
                  onChange={handlePasswordInputChange}
                  placeholder="Confirm new password"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                />
              </div>
            </div>
          )}
        </div>

        {/* Account Information Section */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-800">
              Account Information
            </h2>
            {!isEditingAccount ? (
              <button
                onClick={() => setIsEditingAccount(true)}
                className="text-green-600 hover:text-green-700 text-sm font-medium"
              >
                Edit
              </button>
            ) : (
              <div className="flex gap-2">
                <button
                  onClick={handleAccountSave}
                  disabled={isAccountLoading}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white text-sm font-medium rounded-lg transition-colors flex items-center gap-2"
                >
                  {isAccountLoading && (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  )}
                  {isAccountLoading ? "Saving..." : "Save"}
                </button>
                <button
                  onClick={handleAccountCancel}
                  className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            )}
          </div>

          {accountMessage && (
            <div
              className={`mb-4 p-4 rounded-lg ${
                isAccountSuccess
                  ? "bg-green-100 text-green-700 border border-green-300"
                  : "bg-red-100 text-red-700 border border-red-300"
              }`}
            >
              {accountMessage}
            </div>
          )}

          {isEditingAccount ? (
            <div className="space-y-6">
              <BankSelector
                selectedBank={selectedBank}
                onBankSelect={handleBankSelect}
                disabled={isAccountLoading}
                error={bankErrors.bankName}
                className=""
              />

              <AccountResolver
                accountNumber={accountData.accountNumber}
                selectedBank={selectedBank}
                onAccountNumberChange={handleAccountNumberChange}
                onAccountResolved={handleAccountResolved}
                disabled={isAccountLoading}
                error={bankErrors.accountNumber}
                className=""
              />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Account Number
                </label>
                <div className="px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg">
                  {accountData.accountNumber || "Not set"}
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Your bank account number
                </p>
              </div>

              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Bank Name
                </label>
                <div className="px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg">
                  {accountData.bankName || "Not set"}
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Your bank name
                </p>
              </div>

              {accountData.accountName && (
                <div className="md:col-span-2">
                  <label className="block text-gray-700 font-medium mb-2">
                    Account Name
                  </label>
                  <div className="px-4 py-3 bg-green-50 border border-green-300 rounded-lg">
                    {accountData.accountName}
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Verified account holder name
                  </p>
                </div>
              )}
            </div>
          )}

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-600">
              <strong>Note:</strong> Please Note that all Earnings are received
              by above account details to change Contact Support
            </p>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Settings;
