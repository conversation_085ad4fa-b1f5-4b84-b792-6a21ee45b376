/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { agentApiService } from "../../services/agentApiService";
import { DashboardMetrics, SalesData } from "../../types/agent";
import DashboardLayout from "../../components/Dashboard/DashboardLayout";
import MetricCard from "../../components/Dashboard/MetricCard";
import SalesChart from "../../components/Dashboard/SalesChart";

const AgentDashboard = () => {
  const { state } = useAuth();
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [salesData, setSalesData] = useState<SalesData[]>([]);
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMonth, setSelectedMonth] = useState<number>(
    new Date().getMonth() + 1
  );
  const [selectedYear, setSelectedYear] = useState<number>(
    new Date().getFullYear()
  );

  useEffect(() => {
    const fetchDashboardData = async () => {
      console.log("🔍 Dashboard - Current agent state:", state.agent);
      console.log("🔍 Dashboard - Agent ID:", state.agent?.id);

      if (!state.agent) {
        console.warn("⚠️ Dashboard - No agent data available");
        return;
      }

      if (!state.agent.id) {
        console.error("❌ Dashboard - Agent ID is undefined!");
        setError("Agent ID is missing. Please try logging in again.");
        return;
      }

      setError(null);

      try {
        const [dashboardResponse, metricsResult] = await Promise.all([
          agentApiService.getDashboardData(
            state.agent.id,
            selectedMonth,
            selectedYear
          ),
          agentApiService.getDashboardMetrics(state.agent.id),
        ]);

        if (dashboardResponse.success) {
          setDashboardData(dashboardResponse.data);
          // Use salesHistory from dashboard data
          setSalesData(dashboardResponse.data?.salesHistory || []);
        }
        setMetrics(metricsResult);
        // salesResult is now redundant since we get it from dashboard data
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        setError("Failed to load dashboard data. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [state.agent, selectedMonth, selectedYear]);

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center h-64">
          <div className="text-red-600 text-center mb-4">
            <svg
              className="w-12 h-12 mx-auto mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <p className="text-lg font-medium">{error}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        {dashboardData && (
          <div className="bg-gradient-to-r from-green-600 to-green-700 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold mb-2">
                  Welcome, {dashboardData.name}!
                </h1>
                <p className="text-green-100">
                  Your referral code:{" "}
                  <span className="font-semibold text-white">
                    {dashboardData.referralCode}
                  </span>
                </p>
              </div>
              <div className="text-right">
                <div className="text-3xl mb-2">👋</div>
                <p className="text-sm text-green-100">
                  Ready to grow your network?
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Summary Section */}
        <div>
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            Dashboard Summary
          </h2>

          {metrics && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <MetricCard
                  title="Total Sales"
                  value={`NGN ${metrics.totalSales.toLocaleString()}`}
                  icon="💰"
                  color="green"
                />
                <MetricCard
                  title="Total Referrals"
                  value={metrics.totalReferrals.toString()}
                  icon="👥"
                  color="orange"
                />
                <MetricCard
                  title="Total Referral Earnings"
                  value={`NGN ${metrics.totalReferralEarnings.toLocaleString()}`}
                  icon="💵"
                  color="red"
                />
                <MetricCard
                  title="Expected Earnings"
                  value={`NGN ${metrics.expectedEarnings.toLocaleString()}`}
                  icon="📈"
                  color="green"
                />
              </div>

              {/* Additional Metrics Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <MetricCard
                  title="Orders from Referrals"
                  value={metrics.totalOrdersFromReferrals?.toString() || "0"}
                  icon="📦"
                  color="purple"
                />
                <MetricCard
                  title="Target"
                  value={metrics.target || "Not set"}
                  icon="🎯"
                  color="indigo"
                />
                <MetricCard
                  title="Target Remaining"
                  value={metrics.targetRemaining || "Not specified"}
                  icon="⏳"
                  color="yellow"
                />
              </div>
            </>
          )}
        </div>

        {/* Sales History Section */}
        <div>
          <h2 className="text-xl font-bold text-gray-800 mb-4">Sale History</h2>

          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-green-600 mb-2">
                Sales Growth Over Time
              </h3>
              <div className="flex items-center gap-4 mb-4">
                <div>
                  <p className="text-2xl font-bold text-gray-800">
                    {metrics
                      ? `NGN ${metrics.totalSales.toLocaleString()}`
                      : "NGN 0"}{" "}
                    Total Sales
                  </p>
                  <p className="text-sm text-gray-500">
                    {salesData.length > 0
                      ? `${salesData.length} sales recorded`
                      : "No sales data available"}
                  </p>
                </div>
                <div className="ml-auto flex gap-2">
                  <select
                    value={selectedMonth}
                    onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    {Array.from({ length: 12 }, (_, i) => (
                      <option key={i + 1} value={i + 1}>
                        {new Date(0, i).toLocaleString("default", {
                          month: "long",
                        })}
                      </option>
                    ))}
                  </select>
                  <select
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    {Array.from({ length: 5 }, (_, i) => {
                      const year = new Date().getFullYear() - 2 + i;
                      return (
                        <option key={year} value={year}>
                          {year}
                        </option>
                      );
                    })}
                  </select>
                </div>
              </div>
            </div>

            <SalesChart data={salesData} />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default AgentDashboard;
