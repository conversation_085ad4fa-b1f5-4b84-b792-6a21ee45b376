import { useEffect, useState } from "react";
import Footer from "../../components/Footer";
import Header from "../../components/Header";
import { useLocation } from "react-router-dom";
import BackToTop from "../../components/BackToTop";
import axios from "axios";
import InfoModal from "../../components/Modal/InfoModal";

const Index = () => {
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState({
    show: false,
    info: "",
  });
  const [inputValue, setInputValue] = useState({
    name: "",
    email: "",
    message: "",
  });

  const location = useLocation();

  useEffect(() => {
    const hash = location.hash.replace("#", "");
    if (hash) {
      const section = document.getElementById(hash);
      if (section) {
        section.scrollIntoView({ behavior: "smooth" });
      }
    }
  }, [location]);

  const formValidation = () => {
    let hasErrors = false;
    const newError = {};
    const regexEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (inputValue.name.length < 2) {
      hasErrors = true;
      newError.name = "Please enter valid name";
    }

    if (!regexEmail.test(inputValue.email)) {
      hasErrors = true;
      newError.email = "Please enter valid email address";
    }
    if (inputValue.message.length < 5) {
      hasErrors = true;
      newError.message = "Please enter complete message";
    }
    setErrors(newError);
    return hasErrors;
  };

  const handleSubmit = async () => {
    if (formValidation()) {
      return;
    }
    const payload = {
      name: inputValue.name,
      email: inputValue.email,
      message: inputValue.message,
    };
    setIsLoading((prev) => !prev);
    try {
      const res = await axios.post(
        `${import.meta.env.VITE_API_URL}/contacts`,
        payload
      );
      const resMsg = res.data.message || "Message sent successfully";
      setShowModal((prev) => ({ ...prev, info: resMsg }));
    } catch (error) {
      console.error(error);
      setShowModal({ info: "Something went wrong", show: true });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <section className="max-w-[1450px] mx-auto relative overflow-hidden">
      <Header
        customStyle={"absolute text-white"}
        textColor={"text-white"}
        isOtherPage={true}
      />
      <section
        id="hero"
        className="bg-black relative flex items-center justify-center h-screen"
      >
        <section className="bg-[url('/assets/wave.png')] bg-fixed bg-cover w-full h-full">
          <section
            className="text-white flex h-full flex-col justify-center items-center text-center"
            data-aos="zoom-in-up"
            data-aos-offset="100"
            data-aos-delay="10"
            data-aos-duration="1000"
            data-aos-easing="ease-in-out"
          >
            <h1 className="lg:text-7xl md:text-5xl text-4xl mb-5 font-bold">
              Contact Us
            </h1>
            <p className="text-lg">
              Feel free to reach out to us for any inquiries, requests, or
              support. We are here to assist you!
            </p>
          </section>
        </section>
      </section>

      <section
        id="partnershipForm"
        className="flex flex-col my-10"
        data-aos="fade-up"
        data-aos-offset="100"
        data-aos-delay="10"
        data-aos-duration="1000"
        data-aos-easing="ease-in-out"
      >
        <section>
          <section className="w-3/5 mx-auto">
            <label htmlFor="name">Name</label>
            <input
              className="block border p-4 text-sm w-full rounded-lg my-2"
              type="text"
              name="name"
              id="name"
              value={inputValue.name}
              onChange={(e) =>
                setInputValue((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="Enter your name"
            />
            {errors.name && (
              <p className="text-red-400 text-xs">{errors.name}</p>
            )}
            <label htmlFor="email">Email</label>
            <input
              className="block border p-4 text-sm w-full rounded-lg my-2"
              type="email"
              name="email"
              id="email"
              value={inputValue.email}
              onChange={(e) =>
                setInputValue((prev) => ({ ...prev, email: e.target.value }))
              }
              placeholder="Enter your email address"
            />
            {errors.email && (
              <p className="text-red-400 text-xs">{errors.email}</p>
            )}
            <label htmlFor="message">Message/Request:</label>
            <textarea
              name="message"
              id="message"
              className="block border p-4 text-sm w-full rounded-lg my-2"
              placeholder="If this is a request for account deletion, please tell us your reason for wanting your account deleted."
              value={inputValue.message}
              onChange={(e) =>
                setInputValue((prev) => ({ ...prev, message: e.target.value }))
              }
              rows={8}
            ></textarea>
            {errors.message && (
              <p className="text-red-400 text-xs">{errors.message}</p>
            )}
          </section>
        </section>
        <button
          type="button"
          className="bg-secondary text-white px-10 py-2 w-40 mx-auto my-4"
          onClick={handleSubmit}
        >
          {isLoading ? "..." : "Submit"}
        </button>
        {showModal.show && (
          <InfoModal
            closeModal={() =>
              setShowModal((prev) => ({ ...prev, show: false }))
            }
            info={showModal.info}
          />
        )}
      </section>

      <BackToTop />

      <Footer />
    </section>
  );
};

export default Index;
