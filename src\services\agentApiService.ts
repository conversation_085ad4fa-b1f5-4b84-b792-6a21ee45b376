/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Agent,
  AgentRegistrationData,
  LoginCredentials,
  LoginResponse,
  GetAgentResponse,
  RegistrationResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
  ReferralsResponse,
  DashboardMetrics,
  DashboardResponse,
  SalesData,
  UpdateProfileRequest,
  UpdateProfileResponse,
  ChangePasswordRequest,
  ChangePasswordResponse,
  UpdateAccountInfoRequest,
  UpdateAccountInfoResponse,
} from "../types/agent";
import { apiClient, handleApiResponse, tokenManager } from "./apiClient";

export const agentApiService = {
  // Agent Registration
  async registerAgent(
    data: AgentRegistrationData
  ): Promise<{ success: boolean; message: string; agent?: Agent }> {
    const response = await handleApiResponse(async () => {
      const result = await apiClient.post<RegistrationResponse>(
        "/agents/register",
        data
      );
      return result;
    });

    if (response.success) {
      return {
        success: true,
        message:
          response.data?.message ||
          "Agent registered. Login details sent via email.",
      };
    }

    return {
      success: false,
      message: response.message,
    };
  },

  // Agent Login
  async loginAgent(credentials: LoginCredentials): Promise<{
    success: boolean;
    message: string;
    agent?: Agent;
    token?: string;
  }> {
    const response = await handleApiResponse(async () => {
      const result = await apiClient.post<LoginResponse>(
        "/agents/login",
        credentials
      );
      return result;
    });

    if (response.success && response.data) {
      const { token, agent, message } = response.data;

      // Store the token
      tokenManager.setToken(token);

      // Convert API response format to internal Agent format
      const agentData: Agent = {
        id: agent.id,
        firstname: agent.firstname,
        lastname: agent.lastname,
        email: agent.email,
        category: agent.category,
        referralCode: agent.referralCode, // Capture referral code from API
      };

      return {
        success: true,
        message: message || "Login successful",
        agent: agentData,
        token,
      };
    }
    return {
      success: false,
      message: response.message,
    };
  },

  // Forgot Password
  async forgotPassword(
    data: ForgotPasswordRequest
  ): Promise<{ success: boolean; message: string; email?: string }> {
    const response = await handleApiResponse(async () => {
      const result = await apiClient.post<ForgotPasswordResponse>(
        "/agents/forgot-password",
        data
      );
      return result;
    });

    if (response.success && response.data) {
      return {
        success: true,
        message: "Password reset instructions sent to your email.",
        email: response.data.email,
      };
    }

    return {
      success: false,
      message: response.message,
    };
  },

  // Reset Password
  async resetPassword(
    data: ResetPasswordRequest
  ): Promise<{ success: boolean; message: string }> {
    const response = await handleApiResponse(async () => {
      const result = await apiClient.post<ResetPasswordResponse>(
        "/agents/reset-password",
        data
      );
      return result;
    });

    if (response.success && response.data) {
      return {
        success: true,
        message: response.data.message || "Password successfully reset",
      };
    }

    return {
      success: false,
      message: response.message,
    };
  },

  // Get Agent Referrals
  async getReferrals(
    agentId: string
  ): Promise<{ success: boolean; message: string; data?: ReferralsResponse }> {
    if (!agentId) {
      return {
        success: false,
        message: "Agent ID is required",
      };
    }

    const response = await handleApiResponse(async () => {
      const result = await apiClient.get<ReferralsResponse>(
        `/agents/${agentId}/referrals`
      );
      return result;
    });

    if (response.success && response.data) {
      return {
        success: true,
        message: "Referrals fetched successfully",
        data: response.data,
      };
    }

    return {
      success: false,
      message: response.message,
    };
  },
 // Get Agent Profile Data
  async getAgentById(
    agentId: string
  ): Promise<{ success: boolean; message: string; data?: Agent }> {
    if (!agentId) {
      return {
        success: false,
        message: "Agent ID is required",
      };
    }

    const response = await handleApiResponse(async () => {
      const result = await apiClient.get<GetAgentResponse>(`/agents/${agentId}`);
      return result;
    });

    if (response.success && response.data) {
      const { agent } = response.data;

      console.log("🔍 getAgentById - Raw API response:", response.data);
      console.log("🔍 getAgentById - Agent object:", agent);
      console.log("🔍 getAgentById - Bank info:", agent.bankInfo);

      // Transform API response to internal Agent format
      const agentData: Agent = {
        id: agent.id,
        firstname: agent.firstname,
        lastname: agent.lastname,
        email: agent.email,
        referralCode: agent.referralCode,
        target: agent.target,
        category: agent.category,
        expectedEarnings: agent.expectedEarnings,
        // Handle bank information
        accountNumber: agent.bankInfo?.accountNumber,
        bankName: agent.bankInfo?.bankName,
        bankCode: agent.bankInfo?.bankCode,
        accountName: agent.bankInfo?.accountName,
        bankInfo: agent.bankInfo,
      };

      console.log("🔍 getAgentById - Transformed agent data:", agentData);
      console.log("🔍 getAgentById - Account Number:", agentData.accountNumber);
      console.log("🔍 getAgentById - Bank Name:", agentData.bankName);

      return {
        success: true,
        message: "Agent profile fetched successfully",
        data: agentData,
      };
    }

    return {
      success: false,
      message: response.message,
    };
  },

  // Get Agent Dashboard Data with optional date filtering
  async getDashboardData(
    agentId: string,
    month?: number,
    year?: number
  ): Promise<{ success: boolean; message: string; data?: DashboardResponse }> {
    if (!agentId) {
      return {
        success: false,
        message: "Agent ID is required",
      };
    }

    const response = await handleApiResponse(async () => {
      const params = new URLSearchParams();
      if (month) params.append("month", month.toString());
      if (year) params.append("year", year.toString());

      const url = `/agents/${agentId}/dashboard${
        params.toString() ? `?${params.toString()}` : ""
      }`;
      const result = await apiClient.get<DashboardResponse>(url);
      return result;
    });

    if (response.success && response.data) {
      return {
        success: true,
        message: "Dashboard data fetched successfully",
        data: response.data,
      };
    }

    return {
      success: false,
      message: response.message,
    };
  },

  // Logout (clear token)
  logout(): void {
    tokenManager.removeToken();
  },

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return tokenManager.isTokenValid();
  },

  // Get current token
  getToken(): string | null {
    return tokenManager.getToken();
  },

  // Update Profile (phone number only)
  async updateProfile(
    agentId: string,
    data: UpdateProfileRequest
  ): Promise<{ success: boolean; message: string }> {
    const response = await handleApiResponse(async () => {
      const result = await apiClient.put<UpdateProfileResponse>(
        `/agents/${agentId}/update-profile`,
        data
      );
      return result;
    });

    if (response.success && response.data) {
      return {
        success: true,
        message: response.data.message || "Profile updated successfully",
      };
    }

    return {
      success: false,
      message: response.message,
    };
  },

  // Change Password
  async changePassword(
    agentId: string,
    data: ChangePasswordRequest
  ): Promise<{ success: boolean; message: string }> {
    const response = await handleApiResponse(async () => {
      const result = await apiClient.post<ChangePasswordResponse>(
        `/agents/${agentId}/change-password`,
        data
      );
      return result;
    });

    if (response.success && response.data) {
      return {
        success: true,
        message: response.data.message || "Password changed successfully",
      };
    }

    return {
      success: false,
      message: response.message,
    };
  },

  // Update Account Info
  async updateAccountInfo(
    agentId: string,
    data: UpdateAccountInfoRequest
  ): Promise<{ success: boolean; message: string }> {
    const response = await handleApiResponse(async () => {
      const result = await apiClient.put<UpdateAccountInfoResponse>(
        `/agents/${agentId}/update-account-info`,
        data
      );
      return result;
    });

    if (response.success && response.data) {
      return {
        success: true,
        message:
          response.data.message || "Account information updated successfully",
      };
    }

    return {
      success: false,
      message: response.message,
    };
  },

  // Legacy methods for backward compatibility with existing components
  // These will be updated when we replace the mock service calls

  // Get dashboard metrics (legacy format)
  async getDashboardMetrics(agentId: string): Promise<DashboardMetrics> {
    const response = await this.getDashboardData(agentId);

    if (response.success && response.data) {
      // Transform API response from summary object to flat format
      const { summary } = response.data;
      return {
        totalSales: summary.totalSales || 0,
        totalReferrals: summary.totalReferrals || 0,
        totalReferralEarnings: summary.totalReferralEarnings || 0,
        expectedEarnings: summary.expectedEarnings || 0,
        target: summary.target,
        targetRemaining: summary.targetRemaining,
        totalOrdersFromReferrals: summary.totalOrdersFromReferrals || 0,
      };
    }

    // Return default values if API call fails
    return {
      totalSales: 0,
      totalReferrals: 0,
      totalReferralEarnings: 0,
      expectedEarnings: 0,
      target: "0",
      targetRemaining: "Not specified",
      totalOrdersFromReferrals: 0,
    };
  },

  // Get referrals with pagination (legacy format)
  async getReferralsPaginated(
    agentId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<{ referrals: any[]; total: number }> {
    const response = await this.getReferrals(agentId);

    if (response.success && response.data) {
      const { referrals, totalReferrals } = response.data;

      // Transform API response to legacy format
      const transformedReferrals = referrals.map((ref: { _id: string; firstname: string; lastname: string; email: string }) => ({
        id: ref._id,
        name: `${ref.firstname} ${ref.lastname}`,
        email: ref.email,
        dateReferred: new Date().toLocaleDateString(), // API doesn't provide this yet
      }));

      // Apply pagination (client-side for now)
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedReferrals = transformedReferrals.slice(
        startIndex,
        endIndex
      );

      return {
        referrals: paginatedReferrals,
        total: totalReferrals,
      };
    }

    return {
      referrals: [],
      total: 0,
    };
  },

  // Get sales data from dashboard salesHistory
  async getSalesData(
    agentId: string,
    month?: number,
    year?: number
  ): Promise<SalesData[]> {
    const response = await this.getDashboardData(agentId, month, year);

    if (response.success && response.data) {
      return response.data.salesHistory || [];
    }

    return [];
  },

  // Update agent profile (placeholder - API endpoint not provided)
  async updateAgentProfile(
    _agentId: string,
    _updates: Partial<Agent>
  ): Promise<{ success: boolean; message: string; agent?: Agent }> {
    // This endpoint is not provided in the API specification
    // Return success for now to maintain compatibility
    return {
      success: true,
      message: "Profile update functionality not yet implemented in API",
    };
  },
};
