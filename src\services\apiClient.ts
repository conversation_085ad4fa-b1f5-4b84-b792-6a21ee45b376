/* eslint-disable @typescript-eslint/no-explicit-any */
// API Client configuration and utilities
import { config } from "../config/env";

const BASE_URL = config.apiBaseUrl;

// Token management utilities
export const tokenManager = {
  getToken: (): string | null => {
    return localStorage.getItem("authToken");
  },

  setToken: (token: string): void => {
    localStorage.setItem("authToken", token);
  },

  removeToken: (): void => {
    localStorage.removeItem("authToken");
  },

  isTokenValid: (): boolean => {
    const token = tokenManager.getToken();
    if (!token) return false;

    try {
      // Basic token validation - check if it's not expired
      const payload = JSON.parse(atob(token.split(".")[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp > currentTime;
    } catch {
      return false;
    }
  },
};

// HTTP client with error handling
export class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = BASE_URL) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    // Default headers
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      ...(options.headers as Record<string, string>),
    };

    // Add authorization header if token exists
    const token = tokenManager.getToken();
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    const requestConfig: RequestInit = {
      ...options,
      headers,
    };

    try {
      // Add timeout to the request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), config.apiTimeout);

      const response = await fetch(url, {
        ...requestConfig,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Handle different response statuses
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          errorData.message ||
            `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorData
        );
      }

      // Handle empty responses
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        return await response.json();
      }

      return {} as T;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      // Handle timeout errors
      if (error instanceof Error && error.name === "AbortError") {
        throw new ApiError("Request timeout. Please try again.", 408, {
          originalError: error,
        });
      }

      // Network or other errors
      throw new ApiError(
        "Network error. Please check your connection and try again.",
        0,
        { originalError: error }
      );
    }
  }

  async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: "GET" });
  }

  async post<T>(
    endpoint: string,
    data?: any,
    options?: RequestInit
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(
    endpoint: string,
    data?: any,
    options?: RequestInit
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: "DELETE" });
  }
}

// Custom error class for API errors
export class ApiError extends Error {
  public status: number;
  public data: any;

  constructor(message: string, status: number, data: any = {}) {
    super(message);
    this.name = "ApiError";
    this.status = status;
    this.data = data;
  }

  // Helper methods for common error types
  isNetworkError(): boolean {
    return this.status === 0;
  }

  isAuthError(): boolean {
    return this.status === 401 || this.status === 403;
  }

  isValidationError(): boolean {
    return this.status === 400;
  }

  isServerError(): boolean {
    return this.status >= 500;
  }

  isTimeoutError(): boolean {
    return this.status === 408;
  }
}

// Create a default API client instance
export const apiClient = new ApiClient();

// Response wrapper for consistent API responses
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

// Helper function to handle API responses consistently
export const handleApiResponse = async <T>(
  apiCall: () => Promise<T>
): Promise<{ success: boolean; message: string; data?: T }> => {
  try {
    const data = await apiCall();
    return {
      success: true,
      message: "Operation successful",
      data,
    };
  } catch (error) {
    if (error instanceof ApiError) {
      // Handle authentication errors by clearing token
      if (error.isAuthError()) {
        tokenManager.removeToken();
      }

      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: "An unexpected error occurred. Please try again.",
    };
  }
};
