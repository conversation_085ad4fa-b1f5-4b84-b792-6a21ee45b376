/* eslint-disable @typescript-eslint/no-explicit-any */
import { 
  Bank, 
  GetAllBanksResponse, 
  ResolveAccountRequest, 
  ResolveAccountResponse,
  BankError 
} from "../types/bank";
import { apiClient, handleApiResponse } from "./apiClient";

export const bankService = {
  // Get all available banks
  async getAllBanks(): Promise<{ 
    success: boolean; 
    message: string; 
    data?: Bank[] 
  }> {
    const response = await handleApiResponse(async () => {
      const result = await apiClient.get<GetAllBanksResponse>("/getAllBanks");
      return result;
    });

    if (response.success && response.data) {
      return {
        success: true,
        message: response.data.message || "Banks retrieved successfully",
        data: response.data.data,
      };
    }

    return {
      success: false,
      message: response.message || "Failed to retrieve banks",
    };
  },

  // Resolve account number to get account details
  async resolveAccount(
    accountNumber: string,
    bankCode: string
  ): Promise<{ 
    success: boolean; 
    message: string; 
    data?: {
      account_number: string;
      account_name: string;
      bank_id: number;
    }
  }> {
    const requestData: ResolveAccountRequest = {
      account_number: accountNumber,
      bank_code: bankCode,
    };

    const response = await handleApiResponse(async () => {
      const result = await apiClient.post<ResolveAccountResponse>(
        "/resolveAccount",
        requestData
      );
      return result;
    });

    if (response.success && response.data) {
      const { status, message, data } = response.data;
      
      if (status && data) {
        return {
          success: true,
          message: message || "Account resolved successfully",
          data,
        };
      } else {
        return {
          success: false,
          message: message || "Failed to resolve account",
        };
      }
    }

    return {
      success: false,
      message: response.message || "Failed to resolve account",
    };
  },

  // Validate account number format
  validateAccountNumber(accountNumber: string): string | null {
    if (!accountNumber.trim()) {
      return "Account number is required";
    }

    // Remove any non-digit characters for validation
    const cleanAccountNumber = accountNumber.replace(/\D/g, "");
    
    if (cleanAccountNumber.length < 10) {
      return "Account number must be at least 10 digits";
    }

    if (cleanAccountNumber.length > 12) {
      return "Account number must not exceed 12 digits";
    }

    if (!/^\d+$/.test(cleanAccountNumber)) {
      return "Account number must contain only digits";
    }

    return null;
  },

  // Find bank by code
  findBankByCode(banks: Bank[], bankCode: string): Bank | undefined {
    return banks.find(bank => bank.bank_code === bankCode);
  },

  // Find bank by name
  findBankByName(banks: Bank[], bankName: string): Bank | undefined {
    return banks.find(bank => 
      bank.bank_name.toLowerCase() === bankName.toLowerCase()
    );
  },
};
