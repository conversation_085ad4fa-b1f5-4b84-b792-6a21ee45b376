export interface Agent {
  id: string;
  firstname: string; // Updated to match API response
  lastname: string; // Updated to match API response (was surname)
  email: string;
  category?: "student" | "individual" | "civilServant"; // Updated to match API format
  password?: string;
  contactNumber?: string;
  phoneNumber?: string; // Phone number from API
  referralCode?: string;
  target?: string;
  expectedEarnings?: number;
  accountNumber?: string;
  bankName?: string;
  bankCode?: string;
  accountName?: string;
  bankId?: string;
  bankInfo?: {
    accountNumber: string;
    bankName: string;
    bankCode?: string;
    accountName?: string;
    bankId?: string;
  };
  createdAt?: Date;
}

export interface AgentRegistrationData {
  firstname: string; // Updated to match API request
  lastname: string; // Updated to match API request (was surname)
  email: string;
  category: "student" | "individual" | "civilServant";
}

export interface LoginCredentials {
  email: string;
  password: string;
}

// API Response Types
export interface LoginResponse {
  message: string;
  agent: {
    id: string;
    firstname: string;
    lastname: string;
    email: string;
    referralCode: string;
    category?: "student" | "individual" | "civilServant";
  };
  token: string;
}

// API Response for getting agent profile
export interface GetAgentResponse {
  message: string;
  agent: {
    id: string;
    firstname: string;
    lastname: string;
    email: string;
    referralCode: string;
    target?: string;
    category?: "student" | "individual" | "civilServant";
    phoneNumber?: string;
    expectedEarnings?: number;
    bankInfo?: {
      accountNumber: string;
      bankName: string;
      bankCode?: string;
      accountName?: string;
    };
  };
}

export interface RegistrationResponse {
  message: string;
}

export interface ForgotPasswordResponse {
  email: string;
}

export interface ResetPasswordResponse {
  message: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  email: string;
  otp: string;
  newPassword: string;
}

// Profile Update Types
export interface UpdateProfileRequest {
  phoneNumber: string;
}

export interface UpdateProfileResponse {
  message: string;
}

export interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
}

export interface ChangePasswordResponse {
  message: string;
}

export interface UpdateAccountInfoRequest {
  accountNumber: string;
  bankName: string;
  bankCode?: string;
  accountName?: string;
  bankId?: string;
}

export interface UpdateAccountInfoResponse {
  message: string;
}

export interface DashboardMetrics {
  totalSales: number;
  totalReferrals: number;
  totalReferralEarnings: number;
  expectedEarnings: number; // Updated to match API response
  target?: string;
  targetRemaining?: string;
  totalOrdersFromReferrals?: number;
}

// API Response for dashboard data
export interface DashboardResponse {
  name: string;
  referralCode: string;
  summary: {
    totalSales: number;
    totalReferrals: number;
    totalReferralEarnings: number;
    expectedEarnings: number;
    target: string;
    targetRemaining: string;
    totalOrdersFromReferrals: number;
  };
  salesHistory: SalesData[];
}

export interface Referral {
  _id: string; // Updated to match API response
  firstname: string; // Updated to match API response
  lastname: string; // Updated to match API response
  email: string;
  dateReferred?: string; // Keep for backward compatibility
}

// API Response for referrals
export interface ReferralsResponse {
  agent: string;
  totalReferrals: number;
  referrals: {
    _id: string;
    firstname: string;
    lastname: string;
    email: string;
  }[];
}

export interface SalesData {
  day: string;
  value: number;
}

export interface AuthState {
  isAuthenticated: boolean;
  agent: Agent | null;
  loading: boolean;
}
