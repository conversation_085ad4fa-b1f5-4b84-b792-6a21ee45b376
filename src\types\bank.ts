// Bank-related type definitions

export interface Bank {
  _id: string;
  bank_name: string;
  bank_code: string;
  createdAt: string;
  updatedAt: string;
}

export interface GetAllBanksResponse {
  message: string;
  data: Bank[];
}

export interface ResolveAccountRequest {
  account_number: string;
  bank_code: string;
}

export interface ResolveAccountResponse {
  status: boolean;
  message: string;
  data: {
    account_number: string;
    account_name: string;
    bank_id: number;
  };
}

export interface BankAccountInfo {
  accountNumber: string;
  bankCode: string;
  bankName: string;
  accountName?: string;
  bankId?: string;
}

// Error types for bank operations
export interface BankError {
  message: string;
  code?: string;
}
