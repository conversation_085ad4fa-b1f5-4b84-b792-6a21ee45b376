// Utility functions for handling category display and conversion

export type CategoryValue = "student" | "individual" | "civilServant";

// Map API category values to user-friendly display names
export const categoryDisplayNames: Record<CategoryValue, string> = {
  student: "Student",
  individual: "Individual", 
  civilServant: "Civil Servant"
};

// Convert API category value to display name
export const getCategoryDisplayName = (category?: CategoryValue | null): string => {
  if (!category) return "Not specified";
  return categoryDisplayNames[category] || "Not specified";
};

// Get all category options for forms
export const getCategoryOptions = () => [
  { value: "student", label: "Student" },
  { value: "individual", label: "Individual" },
  { value: "civilServant", label: "Civil Servant" }
];
