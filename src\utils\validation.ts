export interface ValidationRule {
  required?: boolean;
  email?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: string) => string | null;
}

export interface ValidationRules {
  [key: string]: ValidationRule;
}

export interface ValidationErrors {
  [key: string]: string;
}

export const validateField = (
  value: string,
  rules: ValidationRule
): string | null => {
  // Required validation
  if (rules.required && !value.trim()) {
    return "This field is required";
  }

  // Skip other validations if field is empty and not required
  if (!value.trim() && !rules.required) {
    return null;
  }

  // Email validation
  if (rules.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
    return "Please enter a valid email address";
  }

  // Minimum length validation
  if (rules.minLength && value.length < rules.minLength) {
    return `Must be at least ${rules.minLength} characters long`;
  }

  // Maximum length validation
  if (rules.maxLength && value.length > rules.maxLength) {
    return `Must be no more than ${rules.maxLength} characters long`;
  }

  // Pattern validation
  if (rules.pattern && !rules.pattern.test(value)) {
    return "Invalid format";
  }

  // Custom validation
  if (rules.custom) {
    return rules.custom(value);
  }

  return null;
};

export const validateForm = (
  data: { [key: string]: string },
  rules: ValidationRules
): ValidationErrors => {
  const errors: ValidationErrors = {};

  Object.keys(rules).forEach((field) => {
    const value = data[field] || "";
    const fieldRules = rules[field];
    const error = validateField(value, fieldRules);

    if (error) {
      errors[field] = error;
    }
  });

  return errors;
};

// Common validation rules
export const commonRules = {
  required: { required: true },
  email: { required: true, email: true },
  name: { required: true, minLength: 2, maxLength: 50 },
  password: { required: true, minLength: 6 },
  phone: {
    required: true,
    pattern: /^[+]?[1-9][\d]{0,15}$/,
    custom: (value: string) => {
      const cleaned = value.replace(/\D/g, "");
      if (cleaned.length < 10) {
        return "Phone number must be at least 10 digits";
      }
      return null;
    },
  },
};

// Specific validation rules for agent forms
export const agentRegistrationRules: ValidationRules = {
  firstName: commonRules.name,
  surname: commonRules.name,
  email: commonRules.email,
};

export const agentLoginRules: ValidationRules = {
  email: commonRules.email,
  password: commonRules.required,
};

export const agentProfileRules: ValidationRules = {
  firstName: commonRules.name,
  surname: commonRules.name,
  email: commonRules.email,
  contactNumber: commonRules.phone,
  accountNumber: {
    required: true,
    pattern: /^\d{10,12}$/,
    custom: (value: string) => {
      if (!/^\d+$/.test(value)) {
        return "Account number must contain only digits";
      }
      return null;
    },
  },
  bankName: { required: true, minLength: 2, maxLength: 100 },
};

// Bank-specific validation rules
export const bankValidationRules: ValidationRules = {
  accountNumber: {
    required: true,
    pattern: /^\d{10,12}$/,
    custom: (value: string) => {
      const cleaned = value.replace(/\D/g, "");
      if (cleaned.length < 10) {
        return "Account number must be at least 10 digits";
      }
      if (cleaned.length > 12) {
        return "Account number must not exceed 12 digits";
      }
      if (!/^\d+$/.test(cleaned)) {
        return "Account number must contain only digits";
      }
      return null;
    },
  },
  bankCode: {
    required: true,
    pattern: /^\d{3,6}$/,
    custom: (value: string) => {
      if (!/^\d{3,6}$/.test(value)) {
        return "Bank code must be 3-6 digits";
      }
      return null;
    },
  },
  bankName: { required: true, minLength: 2, maxLength: 100 },
  accountName: { required: false, minLength: 2, maxLength: 100 },
};

// Enhanced account info validation
export const validateBankAccountInfo = (
  accountNumber: string,
  bankCode: string,
  bankName: string,
  accountName?: string
): { [key: string]: string } => {
  const errors: { [key: string]: string } = {};

  // Validate account number
  const accountError = validateField(accountNumber, bankValidationRules.accountNumber);
  if (accountError) errors.accountNumber = accountError;

  // Validate bank code
  const bankCodeError = validateField(bankCode, bankValidationRules.bankCode);
  if (bankCodeError) errors.bankCode = bankCodeError;

  // Validate bank name
  const bankNameError = validateField(bankName, bankValidationRules.bankName);
  if (bankNameError) errors.bankName = bankNameError;

  // Validate account name if provided
  if (accountName) {
    const accountNameError = validateField(accountName, bankValidationRules.accountName);
    if (accountNameError) errors.accountName = accountNameError;
  }

  return errors;
};
