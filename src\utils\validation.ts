export interface ValidationRule {
  required?: boolean;
  email?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: string) => string | null;
}

export interface ValidationRules {
  [key: string]: ValidationRule;
}

export interface ValidationErrors {
  [key: string]: string;
}

export const validateField = (
  value: string,
  rules: ValidationRule
): string | null => {
  // Required validation
  if (rules.required && !value.trim()) {
    return "This field is required";
  }

  // Skip other validations if field is empty and not required
  if (!value.trim() && !rules.required) {
    return null;
  }

  // Email validation
  if (rules.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
    return "Please enter a valid email address";
  }

  // Minimum length validation
  if (rules.minLength && value.length < rules.minLength) {
    return `Must be at least ${rules.minLength} characters long`;
  }

  // Maximum length validation
  if (rules.maxLength && value.length > rules.maxLength) {
    return `Must be no more than ${rules.maxLength} characters long`;
  }

  // Pattern validation
  if (rules.pattern && !rules.pattern.test(value)) {
    return "Invalid format";
  }

  // Custom validation
  if (rules.custom) {
    return rules.custom(value);
  }

  return null;
};

export const validateForm = (
  data: { [key: string]: string },
  rules: ValidationRules
): ValidationErrors => {
  const errors: ValidationErrors = {};

  Object.keys(rules).forEach((field) => {
    const value = data[field] || "";
    const fieldRules = rules[field];
    const error = validateField(value, fieldRules);

    if (error) {
      errors[field] = error;
    }
  });

  return errors;
};

// Common validation rules
export const commonRules = {
  required: { required: true },
  email: { required: true, email: true },
  name: { required: true, minLength: 2, maxLength: 50 },
  password: { required: true, minLength: 6 },
  phone: {
    required: true,
    pattern: /^[+]?[1-9][\d]{0,15}$/,
    custom: (value: string) => {
      const cleaned = value.replace(/\D/g, "");
      if (cleaned.length < 10) {
        return "Phone number must be at least 10 digits";
      }
      return null;
    },
  },
};

// Specific validation rules for agent forms
export const agentRegistrationRules: ValidationRules = {
  firstName: commonRules.name,
  surname: commonRules.name,
  email: commonRules.email,
};

export const agentLoginRules: ValidationRules = {
  email: commonRules.email,
  password: commonRules.required,
};

export const agentProfileRules: ValidationRules = {
  firstName: commonRules.name,
  surname: commonRules.name,
  email: commonRules.email,
  contactNumber: commonRules.phone,
  accountNumber: {
    required: true,
    pattern: /^\d{10,12}$/,
    custom: (value: string) => {
      if (!/^\d+$/.test(value)) {
        return "Account number must contain only digits";
      }
      return null;
    },
  },
  bankName: { required: true, minLength: 2, maxLength: 100 },
};
