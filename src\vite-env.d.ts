/// <reference types="vite/client" />

interface ImportMetaEnv {
  // Agent Portal API URL - PRIMARY for all agent functionality
  readonly REACT_APP_API_URL?: string;

  // Other environment variables (NOT used for agent portal)
  readonly VITE_API_URL?: string;
  readonly VITE_NODE_ENV?: string;
  readonly VITE_API_TIMEOUT?: string;
  readonly DEV: boolean;
  readonly PROD: boolean;
  readonly MODE: string;
  readonly BASE_URL: string;
  readonly SSR: boolean;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
