/** @type {import('tailwindcss').Config} */
import defaultTheme from "tailwindcss/defaultTheme";

export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        secondary: "#008B50",
        grey: "#585858",
      },
      fontFamily: {
        ppins: ['"Poppins"', ...defaultTheme.fontFamily.serif],
        inter: ['"Inter"', ...defaultTheme.fontFamily.serif],
        mplus: ['"M PLUS 1"', ...defaultTheme.fontFamily.serif],
        alt: ["DM Sans", "sans-serif"],
      },
      animation: {
        openmenu: "openmenu 1s ease-in",
        closemenu: "closemenu 1s ease-in",
      },
      keyframes: {
        openmenu: {
          "0%": { left: "-224px" },
          "100%": { left: "0px" },
        },
        closemenu: {
          "0%": { left: "0px" },
          "100%": { left: "-224px" },
        },
      },
      backgroundImage: {
        heroSection: "url('/assets/cheerful.png')",
        scanCodeImg: "url('/assets/ellipse.svg')",
        footerImg: 'url("/assets/footerImg.jpeg")',
      },
      backgroundSize: {
        "50%": "50%",
      },
      backgroundPosition: {
        "right-bottom": "right bottom",
        "middle": "right 30%",
        "middle2": "right 10%"
      },
    },
  },
  plugins: [],
};
